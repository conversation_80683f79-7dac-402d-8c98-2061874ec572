# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
.expo-shared/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env

# prod env files
.env*.production

# typescript
*.tsbuildinfo

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/Podfile.lock
ios/DerivedData/
ios/Seascape.xcworkspace/
ios/Seascape.xcodeproj/project.xcworkspace/
ios/Seascape.xcodeproj/xcuserdata/
ios/Seascape.xcodeproj/project.pbxproj
ios/Seascape/Images.xcassets/

# Android
android/.gradle/
android/.idea/
android/build/
android/app/build/
android/local.properties

# Common
*.log
*.lock
*.xcuserstate
*.iml

# EAS/Expo
eas.json

# @generated expo-cli sync-8d4afeec25ea8a192358fae2f8e2fc766bdce4ec
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @end expo-cli