{"name": "seascape", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hcaptcha/react-hcaptcha": "^1.12.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.50.0", "aes-js": "^3.1.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "53.0.11", "expo-audio": "~0.4.6", "expo-auth-session": "~6.2.0", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.0", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-local-authentication": "~16.0.4", "expo-router": "~5.1.0", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.517.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-get-random-values": "^1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.6.4", "uri-scheme": "^1.4.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react": "^16.3.0", "@testing-library/react-native": "^13.2.0", "@types/aes-js": "^3.1.4", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "eas-cli": "^16.10.1", "jest": "^29.7.0", "jest-date-mock": "^1.0.10", "jest-expo": "~53.0.7", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@4.9.2"}